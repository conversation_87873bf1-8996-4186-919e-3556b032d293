import subprocess
from datetime import datetime

def call_gemini_cli(prompt):
    """
    使用给定的提示调用 Gemini CLI 并返回其输出。

    Args:
        prompt: 要发送到 Gemini CLI 的字符串提示。

    Returns:
        成功时为 Gemini CLI 的输出字符串，否则为错误信息。
    """
    try:
        # 构建要执行的命令
        command = ["gemini","-p", prompt]

        # 使用 subprocess.run 执行命令
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            encoding='utf-8',
            check=True,  # 如果命令返回非零退出代码，则引发 CalledProcessError
            shell=True
        )

        # 返回捕获的标准输出
        return result.stdout
    except FileNotFoundError:
        return "错误：'gemini' 命令未找到。请确保 Gemini CLI 已安装并位于您的系统 PATH 中。"
    except subprocess.CalledProcessError as e:
        # 如果命令执行失败，返回错误信息
        error_message = f"Gemini CLI 执行失败。\n"
        error_message += f"返回码: {e.returncode}\n"
        error_message += f"输出: {e.stdout}\n"
        error_message += f"错误: {e.stderr}"
        return error_message

# 调用 Gemini CLI 的示例
# 动态获取今天的日期
today = datetime.now().strftime("%Y年%m月%d日")
# 在提示词中添加今天的日期
prompt = f"今天是{today}。请检索最近一周内新推出的AI编程工具，并提供官网链接。注意：1、请确认今天的日期与工具推出的日期；2、如果你无法进行互联网搜索，请告诉我，不要虚构信息。"
output = call_gemini_cli(prompt)
print(output)