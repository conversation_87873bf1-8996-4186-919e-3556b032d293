#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI编程工具信息抓取器
每日获取最新的AI编程工具信息，通过Gemini验证并保存结果
"""

import os
import json
import requests
import time
from datetime import datetime, timedelta
from google import genai
import logging
from dotenv import load_dotenv
import config
import subprocess
from urllib.parse import urlparse
import re

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_tools_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class AIToolsCrawler:
    def __init__(self):
        """初始化爬虫"""
        # API配置 - 请在环境变量中设置这些值
        self.google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
        self.search_engine_id = os.getenv('GOOGLE_SEARCH_ENGINE_ID')
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        
        if not all([self.google_api_key, self.search_engine_id, self.gemini_api_key]):
            raise ValueError("请设置环境变量: GOOGLE_SEARCH_API_KEY, GOOGLE_SEARCH_ENGINE_ID, GEMINI_API_KEY")
        
        # 初始化Gemini客户端
        self.gemini_client = genai.Client()

        # 从配置文件加载参数
        self.search_keywords = config.SEARCH_KEYWORDS
        self.batch_size = config.BATCH_SIZE
        self.search_days_back = config.SEARCH_DAYS_BACK
        self.search_results_per_keyword = config.SEARCH_RESULTS_PER_KEYWORD
        self.search_delay = config.SEARCH_DELAY
        self.analysis_delay = config.ANALYSIS_DELAY
        self.gemini_model = config.GEMINI_MODEL

        # Google Custom Search API基础URL
        self.search_url = "https://www.googleapis.com/customsearch/v1"

        # AI分析方式配置
        self.use_genai_primary = True  # 优先使用genai.py方式
        self.use_cli_fallback = True   # 备选使用CLI方式
        
    def search_google(self, query, days_back=None):
        """
        使用Google Custom Search API搜索
        
        Args:
            query: 搜索关键词
            days_back: 搜索最近几天的内容
            
        Returns:
            搜索结果列表
        """
        try:
            if days_back is None:
                days_back = self.search_days_back

            params = {
                'key': self.google_api_key,
                'cx': self.search_engine_id,
                'q': query,
                'num': self.search_results_per_keyword,
                'dateRestrict': f'd{days_back}',  # 限制最近N天
                'sort': 'date',  # 按日期排序
                'lr': 'lang_en',  # 主要搜索英文内容
            }
            
            response = requests.get(self.search_url, params=params)
            response.raise_for_status()
            
            data = response.json()
            results = []
            
            if 'items' in data:
                for item in data['items']:
                    result = {
                        'title': item.get('title', ''),
                        'link': item.get('link', ''),
                        'snippet': item.get('snippet', ''),
                        'displayLink': item.get('displayLink', ''),
                        'formattedUrl': item.get('formattedUrl', '')
                    }
                    results.append(result)
                    
            logging.info(f"搜索关键词 '{query}' 找到 {len(results)} 个结果")
            return results
            
        except requests.exceptions.RequestException as e:
            logging.error(f"搜索请求失败: {e}")
            return []
        except Exception as e:
            logging.error(f"搜索过程出错: {e}")
            return []
    
    def save_raw_results(self, all_results):
        """
        保存原始搜索结果到文件

        Args:
            all_results: 所有搜索结果列表
        """
        today = datetime.now().strftime("%Y%m%d")
        filename = f"raw_search_results_{today}.txt"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"原始搜索结果 - {datetime.now().strftime('%Y年%m月%d日')}\n")
                f.write("=" * 50 + "\n\n")

                for i, result in enumerate(all_results, 1):
                    f.write(f"{i}. {result['title']}\n")
                    f.write(f"链接: {result['link']}\n")
                    f.write(f"摘要: {result['snippet']}\n")
                    f.write(f"来源: {result['displayLink']}\n")
                    f.write("-" * 40 + "\n\n")

                f.write(f"\n总计找到 {len(all_results)} 条搜索结果\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            logging.info(f"原始搜索结果已保存到: {filename}")

        except Exception as e:
            logging.error(f"保存原始结果失败: {e}")

    def fetch_webpage_content(self, url, max_length=2000):
        """
        尝试获取网页内容

        Args:
            url: 网页链接
            max_length: 最大内容长度

        Returns:
            str: 网页内容或错误信息
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            # 简单提取文本内容（去除HTML标签）
            content = response.text
            # 移除HTML标签
            content = re.sub(r'<[^>]+>', ' ', content)
            # 移除多余空白
            content = re.sub(r'\s+', ' ', content).strip()

            # 限制长度
            if len(content) > max_length:
                content = content[:max_length] + "..."

            return content

        except requests.exceptions.RequestException as e:
            return f"无法访问网页: {str(e)}"
        except Exception as e:
            return f"获取网页内容失败: {str(e)}"

    def call_gemini_with_genai(self, prompt):
        """
        使用genai.py方式调用Gemini（优先方式）

        Args:
            prompt: 提示词

        Returns:
            str: Gemini回复或错误信息
        """
        try:
            response = self.gemini_client.models.generate_content(
                model=self.gemini_model,
                contents=prompt
            )
            return response.text
        except Exception as e:
            logging.error(f"genai方式调用失败: {e}")
            return None

    def call_gemini_with_cli(self, prompt):
        """
        使用CLI方式调用Gemini（备选方式）

        Args:
            prompt: 提示词

        Returns:
            str: Gemini回复或错误信息
        """
        try:
            command = ["gemini", "-p", prompt]
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                encoding='utf-8',
                check=True,
                shell=True,
                timeout=60
            )
            return result.stdout
        except subprocess.TimeoutExpired:
            logging.error("CLI调用超时")
            return None
        except FileNotFoundError:
            logging.error("Gemini CLI未找到")
            return None
        except subprocess.CalledProcessError as e:
            logging.error(f"CLI调用失败: {e}")
            return None
        except Exception as e:
            logging.error(f"CLI调用异常: {e}")
            return None

    def call_gemini_robust(self, prompt):
        """
        稳健的Gemini调用方法（优先genai，备选CLI）

        Args:
            prompt: 提示词

        Returns:
            str: Gemini回复
        """
        # 优先使用genai方式
        if self.use_genai_primary:
            result = self.call_gemini_with_genai(prompt)
            if result:
                return result
            logging.warning("genai方式失败，尝试CLI方式...")

        # 备选使用CLI方式
        if self.use_cli_fallback:
            result = self.call_gemini_with_cli(prompt)
            if result:
                return result
            logging.error("CLI方式也失败")

        raise Exception("所有Gemini调用方式都失败")

    def analyze_batch_with_gemini(self, results_batch):
        """
        使用Gemini批量分析多条资讯（增强版，包含网页内容获取）

        Args:
            results_batch: 资讯列表，每个元素包含title, snippet, link

        Returns:
            list: 分析结果列表
        """
        try:
            # 构建增强的资讯列表（包含网页内容）
            news_list = ""
            webpage_access_status = []

            for i, result in enumerate(results_batch, 1):
                logging.info(f"正在获取第{i}条资讯的网页内容: {result['title'][:30]}...")

                # 尝试获取网页内容
                webpage_content = self.fetch_webpage_content(result['link'])

                if "无法访问网页" in webpage_content or "获取网页内容失败" in webpage_content:
                    webpage_access_status.append(f"资讯{i}: 无法访问")
                    news_list += f"""
资讯 {i}:
标题: {result['title']}
摘要: {result['snippet']}
链接: {result['link']}
网页内容: 无法访问网页内容
---
"""
                else:
                    webpage_access_status.append(f"资讯{i}: 成功访问")
                    news_list += f"""
资讯 {i}:
标题: {result['title']}
摘要: {result['snippet']}
链接: {result['link']}
网页内容: {webpage_content}
---
"""

            # 改进的提示词模板
            enhanced_prompt = f"""请分析以下多条资讯，判断哪些是关于AI编程工具的信息。请用中文回复。

网页访问状态:
{chr(10).join(webpage_access_status)}

以下是需要分析的资讯列表：

{news_list}

请对每条资讯按照以下格式分析（请严格按照编号顺序回复，确保格式完整）：

资讯X分析:
1. 是否为AI编程工具: [是/否]
2. 工具类型: [如果是AI编程工具，请说明类型，如代码生成、代码补全、IDE插件等；如果不是请写"不适用"]
3. 工具名称: [如果是AI编程工具，请提取具体的工具名称；如果不是请写"不适用"]
4. 主要功能: [简要描述主要功能，如果内容是英文请翻译为中文；如果不是AI编程工具请写"不适用"]
5. 是否值得关注: [是/否，说明理由]
6. 简要总结: [用1-2句话总结，如果不是AI编程工具请说明原因]

重要要求：
- 请确保回复内容为中文，如果原文是英文请翻译为中文
- 请严格按照"资讯1分析:"、"资讯2分析:"的格式分隔每条分析结果
- 每条分析必须包含完整的6个要点
- 如果无法访问网页内容，请在分析中明确说明
- 请尽量从标题、摘要和网页内容中提取准确的工具名称
"""

            # 使用稳健的调用方法
            analysis_text = self.call_gemini_robust(enhanced_prompt)

            # 解析批量分析结果
            return self.parse_batch_analysis_enhanced(analysis_text, results_batch, webpage_access_status)

        except Exception as e:
            logging.error(f"Gemini批量分析失败: {e}")
            # 返回失败结果
            return [{
                'is_ai_tool': False,
                'analysis': f"分析失败: {str(e)}",
                'title': result['title'],
                'url': result['link'],
                'snippet': result['snippet'],
                'tool_name': '未知',
                'webpage_accessible': False
            } for result in results_batch]

    def parse_batch_analysis_enhanced(self, analysis_text, results_batch, webpage_access_status):
        """
        解析增强版批量分析结果

        Args:
            analysis_text: Gemini返回的分析文本
            results_batch: 原始资讯列表
            webpage_access_status: 网页访问状态列表

        Returns:
            list: 解析后的结果列表
        """
        try:
            parsed_results = []

            # 按照"资讯X分析:"分割文本
            sections = analysis_text.split('资讯')[1:]  # 去掉第一个空元素

            for i, section in enumerate(sections):
                if i >= len(results_batch):
                    break

                result = results_batch[i]

                # 检查是否为AI编程工具
                is_ai_tool = False
                tool_name = "未知"
                webpage_accessible = "成功访问" in webpage_access_status[i] if i < len(webpage_access_status) else False

                # 解析各个字段
                lines = section.split('\n')
                for line in lines:
                    line = line.strip()
                    if '是否为AI编程工具:' in line and '是' in line:
                        is_ai_tool = True
                    elif '工具名称:' in line:
                        # 提取工具名称
                        tool_name_match = line.split('工具名称:')[1].strip()
                        if tool_name_match and tool_name_match != "不适用":
                            tool_name = tool_name_match

                # 确保分析内容完整
                if len(section.strip()) < 50:  # 如果分析内容太短，可能不完整
                    section += "\n注意: 分析内容可能不完整，请检查原始数据。"

                parsed_result = {
                    'is_ai_tool': is_ai_tool,
                    'analysis': f"资讯{i+1}分析:\n{section.strip()}",
                    'title': result['title'],
                    'url': result['link'],
                    'snippet': result['snippet'],
                    'tool_name': tool_name,
                    'webpage_accessible': webpage_accessible
                }

                parsed_results.append(parsed_result)

            # 如果解析的结果数量不够，补充剩余的
            while len(parsed_results) < len(results_batch):
                idx = len(parsed_results)
                parsed_results.append({
                    'is_ai_tool': False,
                    'analysis': "解析失败或分析不完整",
                    'title': results_batch[idx]['title'],
                    'url': results_batch[idx]['link'],
                    'snippet': results_batch[idx]['snippet'],
                    'tool_name': '未知',
                    'webpage_accessible': False
                })

            return parsed_results

        except Exception as e:
            logging.error(f"解析增强版批量分析结果失败: {e}")
            # 返回默认结果
            return [{
                'is_ai_tool': False,
                'analysis': f"解析失败: {str(e)}",
                'title': result['title'],
                'url': result['link'],
                'snippet': result['snippet'],
                'tool_name': '未知',
                'webpage_accessible': False
            } for result in results_batch]

    def parse_batch_analysis(self, analysis_text, results_batch):
        """
        解析批量分析结果

        Args:
            analysis_text: Gemini返回的分析文本
            results_batch: 原始资讯列表

        Returns:
            list: 解析后的结果列表
        """
        try:
            parsed_results = []

            # 按照"资讯X分析:"分割文本
            sections = analysis_text.split('资讯')[1:]  # 去掉第一个空元素

            for i, section in enumerate(sections):
                if i >= len(results_batch):
                    break

                result = results_batch[i]

                # 检查是否为AI编程工具
                is_ai_tool = False
                if '是否为AI编程工具:' in section:
                    ai_tool_line = [line for line in section.split('\n') if '是否为AI编程工具:' in line]
                    if ai_tool_line and '是' in ai_tool_line[0]:
                        is_ai_tool = True

                parsed_result = {
                    'is_ai_tool': is_ai_tool,
                    'analysis': f"资讯{i+1}分析:\n{section.strip()}",
                    'title': result['title'],
                    'url': result['link'],
                    'snippet': result['snippet']
                }

                parsed_results.append(parsed_result)

            # 如果解析的结果数量不够，补充剩余的
            while len(parsed_results) < len(results_batch):
                idx = len(parsed_results)
                parsed_results.append({
                    'is_ai_tool': False,
                    'analysis': "解析失败",
                    'title': results_batch[idx]['title'],
                    'url': results_batch[idx]['link'],
                    'snippet': results_batch[idx]['snippet']
                })

            return parsed_results

        except Exception as e:
            logging.error(f"解析批量分析结果失败: {e}")
            # 返回默认结果
            return [{
                'is_ai_tool': False,
                'analysis': f"解析失败: {str(e)}",
                'title': result['title'],
                'url': result['link'],
                'snippet': result['snippet']
            } for result in results_batch]
    
    def save_results(self, verified_tools):
        """
        保存验证通过的工具信息到文件（增强版，包含工具名称汇总）

        Args:
            verified_tools: 验证通过的工具列表
        """
        if not verified_tools:
            logging.info("没有找到符合条件的AI编程工具")
            return

        # 生成文件名（包含日期）
        today = datetime.now().strftime("%Y%m%d")
        filename = f"ai_tools_{today}.txt"

        try:
            # 统计工具名称和网页访问情况
            tool_names = []
            accessible_count = 0
            inaccessible_count = 0

            for tool in verified_tools:
                if tool.get('tool_name') and tool['tool_name'] != '未知':
                    tool_names.append(tool['tool_name'])

                if tool.get('webpage_accessible', False):
                    accessible_count += 1
                else:
                    inaccessible_count += 1

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"AI编程工具信息汇总 - {datetime.now().strftime('%Y年%m月%d日')}\n")
                f.write("=" * 50 + "\n\n")

                # 工具名称汇总
                f.write("🔧 发现的AI编程工具名称汇总:\n")
                if tool_names:
                    for i, name in enumerate(set(tool_names), 1):  # 去重
                        f.write(f"{i}. {name}\n")
                else:
                    f.write("未能提取到具体工具名称\n")
                f.write("\n")

                # 网页访问统计
                f.write("📊 网页访问统计:\n")
                f.write(f"成功访问: {accessible_count} 个\n")
                f.write(f"无法访问: {inaccessible_count} 个\n")
                f.write(f"访问成功率: {accessible_count/(accessible_count+inaccessible_count)*100:.1f}%\n\n")

                # 详细信息
                f.write("📝 详细分析结果:\n")
                f.write("=" * 50 + "\n\n")

                for i, tool in enumerate(verified_tools, 1):
                    f.write(f"{i}. {tool['title']}\n")
                    f.write(f"链接: {tool['url']}\n")
                    f.write(f"工具名称: {tool.get('tool_name', '未知')}\n")
                    f.write(f"网页可访问: {'是' if tool.get('webpage_accessible', False) else '否'}\n")
                    f.write(f"分析结果:\n{tool['analysis']}\n")
                    f.write("-" * 40 + "\n\n")

                f.write(f"\n📈 统计信息:\n")
                f.write(f"总计找到 {len(verified_tools)} 个AI编程工具\n")
                f.write(f"提取到工具名称 {len(set(tool_names))} 个\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            logging.info(f"结果已保存到文件: {filename}")
            logging.info(f"工具名称汇总: {', '.join(set(tool_names)) if tool_names else '无'}")

        except Exception as e:
            logging.error(f"保存文件失败: {e}")
    
    def run(self):
        """运行主程序"""
        logging.info("开始抓取AI编程工具信息...")

        all_results = []
        verified_tools = []

        # 第一步：搜索所有关键词
        logging.info("第一步：搜索资讯...")
        for keyword in self.search_keywords:
            logging.info(f"正在搜索: {keyword}")

            # 搜索
            search_results = self.search_google(keyword)
            all_results.extend(search_results)

            # 避免API调用过于频繁
            time.sleep(self.search_delay)

        # 去重（基于URL）
        unique_results = {}
        for result in all_results:
            url = result['link']
            if url not in unique_results:
                unique_results[url] = result

        unique_results_list = list(unique_results.values())
        logging.info(f"去重后共有 {len(unique_results_list)} 个唯一结果")

        # 第二步：保存原始搜索结果
        logging.info("第二步：保存原始搜索结果...")
        self.save_raw_results(unique_results_list)

        # 第三步：批量分析
        logging.info(f"第三步：批量分析（每批 {self.batch_size} 条）...")

        # 分批处理
        for i in range(0, len(unique_results_list), self.batch_size):
            batch = unique_results_list[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            total_batches = (len(unique_results_list) + self.batch_size - 1) // self.batch_size

            logging.info(f"正在分析第 {batch_num}/{total_batches} 批（{len(batch)} 条资讯）...")

            # 批量分析
            batch_results = self.analyze_batch_with_gemini(batch)

            # 筛选AI编程工具
            for result in batch_results:
                if result['is_ai_tool']:
                    verified_tools.append(result)
                    logging.info(f"✓ 发现AI编程工具: {result['title'][:50]}...")

            # 避免API调用过于频繁
            if i + self.batch_size < len(unique_results_list):
                logging.info(f"等待{self.analysis_delay}秒后处理下一批...")
                time.sleep(self.analysis_delay)

        # 第四步：保存最终结果
        logging.info("第四步：保存分析结果...")
        self.save_results(verified_tools)

        logging.info(f"抓取完成！共发现 {len(verified_tools)} 个AI编程工具")
        logging.info(f"Gemini API调用次数: {(len(unique_results_list) + self.batch_size - 1) // self.batch_size} 次")
        return verified_tools

def main():
    """主函数"""
    try:
        crawler = AIToolsCrawler()
        results = crawler.run()
        
        print(f"\n抓取完成！")
        print(f"共发现 {len(results)} 个AI编程工具")
        print(f"详细信息已保存到文件中")
        
    except Exception as e:
        logging.error(f"程序运行失败: {e}")
        print(f"程序运行失败: {e}")

if __name__ == "__main__":
    main()
