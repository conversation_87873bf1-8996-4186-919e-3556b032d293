from google import genai
from datetime import datetime

# The client gets the API key from the environment variable `GEMINI_API_KEY`.
client = genai.Client()

# 动态获取今天的日期
today = datetime.now().strftime("%Y年%m月%d日")

# 在提示词中添加今天的日期
prompt = f"今天是{today}。请检索最近一周内新推出的AI编程工具，并提供官网链接。注意，如果你无法进行互联网搜索，请告诉我，不要虚构信息"

response = client.models.generate_content(
    model="gemini-2.5-pro", contents=prompt
)
print(response.text)