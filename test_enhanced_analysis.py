#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强分析功能测试脚本
测试网页内容获取、工具名称提取和稳健的AI调用功能
"""

import os
from dotenv import load_dotenv
from ai_tools_crawler import AIToolsCrawler

# 加载环境变量
load_dotenv()

def test_webpage_content_fetch():
    """测试网页内容获取功能"""
    print("🌐 测试网页内容获取功能...")
    
    crawler = AIToolsCrawler()
    
    test_urls = [
        "https://github.com/features/copilot",
        "https://cursor.sh",
        "https://invalid-url-that-does-not-exist.com"
    ]
    
    for url in test_urls:
        print(f"\n测试URL: {url}")
        content = crawler.fetch_webpage_content(url, max_length=500)
        print(f"结果: {content[:100]}...")
        
        if "无法访问网页" in content:
            print("❌ 无法访问")
        else:
            print("✅ 成功获取内容")

def test_gemini_calling_methods():
    """测试不同的Gemini调用方式"""
    print("\n🤖 测试Gemini调用方式...")
    
    crawler = AIToolsCrawler()
    test_prompt = "请用中文简单介绍一下AI编程工具的作用。"
    
    # 测试genai方式
    print("\n1. 测试genai方式:")
    try:
        result = crawler.call_gemini_with_genai(test_prompt)
        if result:
            print("✅ genai方式成功")
            print(f"回复: {result[:100]}...")
        else:
            print("❌ genai方式失败")
    except Exception as e:
        print(f"❌ genai方式异常: {e}")
    
    # 测试CLI方式
    print("\n2. 测试CLI方式:")
    try:
        result = crawler.call_gemini_with_cli(test_prompt)
        if result:
            print("✅ CLI方式成功")
            print(f"回复: {result[:100]}...")
        else:
            print("❌ CLI方式失败")
    except Exception as e:
        print(f"❌ CLI方式异常: {e}")
    
    # 测试稳健调用方式
    print("\n3. 测试稳健调用方式:")
    try:
        result = crawler.call_gemini_robust(test_prompt)
        print("✅ 稳健调用成功")
        print(f"回复: {result[:100]}...")
    except Exception as e:
        print(f"❌ 稳健调用失败: {e}")

def test_enhanced_batch_analysis():
    """测试增强的批量分析功能"""
    print("\n📊 测试增强的批量分析功能...")
    
    crawler = AIToolsCrawler()
    
    # 测试数据
    test_data = [
        {
            'title': 'GitHub Copilot X - AI-powered developer experience',
            'snippet': 'GitHub Copilot X is the vision for the future of AI-powered software development.',
            'link': 'https://github.com/features/copilot'
        },
        {
            'title': 'Cursor - The AI-first Code Editor',
            'snippet': 'Cursor is an AI-first code editor designed for pair-programming with AI.',
            'link': 'https://cursor.sh'
        }
    ]
    
    print(f"测试数据: {len(test_data)} 条")
    
    try:
        results = crawler.analyze_batch_with_gemini(test_data)
        
        print(f"\n分析结果: {len(results)} 条")
        
        for i, result in enumerate(results, 1):
            print(f"\n--- 结果 {i} ---")
            print(f"标题: {result['title'][:50]}...")
            print(f"是否AI工具: {result['is_ai_tool']}")
            print(f"工具名称: {result.get('tool_name', '未知')}")
            print(f"网页可访问: {result.get('webpage_accessible', False)}")
            print(f"分析内容: {result['analysis'][:200]}...")
        
        # 统计
        ai_tools_count = sum(1 for r in results if r['is_ai_tool'])
        accessible_count = sum(1 for r in results if r.get('webpage_accessible', False))
        
        print(f"\n📈 统计:")
        print(f"AI工具数量: {ai_tools_count}/{len(results)}")
        print(f"网页可访问: {accessible_count}/{len(results)}")
        
        if ai_tools_count > 0:
            print("✅ 增强批量分析功能正常")
        else:
            print("⚠️ 未识别出AI工具，请检查分析逻辑")
            
    except Exception as e:
        print(f"❌ 增强批量分析失败: {e}")

def test_tool_name_extraction():
    """测试工具名称提取功能"""
    print("\n🔧 测试工具名称提取功能...")
    
    # 模拟分析结果
    test_analysis = """资讯1分析:
1. 是否为AI编程工具: 是
2. 工具类型: AI代码生成和补全工具
3. 工具名称: GitHub Copilot X
4. 主要功能: 基于GPT-4的智能代码生成
5. 是否值得关注: 是
6. 简要总结: GitHub推出的新一代AI编程助手

资讯2分析:
1. 是否为AI编程工具: 是
2. 工具类型: AI代码编辑器
3. 工具名称: Cursor
4. 主要功能: AI优先的代码编辑器
5. 是否值得关注: 是
6. 简要总结: 专为AI配对编程设计的编辑器"""
    
    crawler = AIToolsCrawler()
    
    # 模拟数据
    test_batch = [
        {'title': 'GitHub Copilot X', 'link': 'https://github.com/features/copilot', 'snippet': 'AI tool'},
        {'title': 'Cursor Editor', 'link': 'https://cursor.sh', 'snippet': 'AI editor'}
    ]
    
    webpage_status = ['资讯1: 成功访问', '资讯2: 成功访问']
    
    try:
        results = crawler.parse_batch_analysis_enhanced(test_analysis, test_batch, webpage_status)
        
        print("解析结果:")
        for i, result in enumerate(results, 1):
            print(f"{i}. 工具名称: {result.get('tool_name', '未知')}")
            print(f"   是否AI工具: {result['is_ai_tool']}")
            print(f"   网页可访问: {result.get('webpage_accessible', False)}")
        
        tool_names = [r.get('tool_name', '未知') for r in results if r.get('tool_name') != '未知']
        print(f"\n提取到的工具名称: {', '.join(tool_names)}")
        
        if tool_names:
            print("✅ 工具名称提取功能正常")
        else:
            print("❌ 工具名称提取失败")
            
    except Exception as e:
        print(f"❌ 工具名称提取测试失败: {e}")

def main():
    """主函数"""
    print("=== 增强分析功能测试 ===\n")
    
    # 检查API密钥
    if not os.getenv('GEMINI_API_KEY'):
        print("❌ GEMINI_API_KEY 未设置，请先配置环境变量")
        return
    
    # 运行各项测试
    test_webpage_content_fetch()
    test_gemini_calling_methods()
    test_enhanced_batch_analysis()
    test_tool_name_extraction()
    
    print("\n=== 测试完成 ===")
    print("💡 如果所有测试通过，可以运行完整的抓取程序")

if __name__ == "__main__":
    main()
